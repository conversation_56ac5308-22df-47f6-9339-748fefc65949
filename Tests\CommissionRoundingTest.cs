using System;
using Xunit;

namespace Tests
{
    /// <summary>
    /// Test to demonstrate and verify the commission rounding fix
    /// </summary>
    public class CommissionRoundingTest
    {
        [Theory]
        [InlineData(100.4, 100)] // Should round down
        [InlineData(100.5, 101)] // Should round up (away from zero)
        [InlineData(100.6, 101)] // Should round up
        [InlineData(99.4, 99)]   // Should round down
        [InlineData(99.5, 100)]  // Should round up (away from zero)
        [InlineData(99.6, 100)]  // Should round up
        [InlineData(0.4, 0)]     // Should round down
        [InlineData(0.5, 1)]     // Should round up (away from zero)
        [InlineData(0.6, 1)]     // Should round up
        public void TestCommissionRounding_ShouldRoundCorrectly(decimal input, long expected)
        {
            // Arrange & Act
            // This simulates the fixed rounding logic in AffiliationPartnerRepository
            long result = (long)Math.Round(input, MidpointRounding.AwayFromZero);
            
            // Assert
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData(100.4, 100)] // Truncates to 100 (incorrect)
        [InlineData(100.5, 100)] // Truncates to 100 (incorrect, should be 101)
        [InlineData(100.6, 100)] // Truncates to 100 (incorrect, should be 101)
        [InlineData(99.4, 99)]   // Truncates to 99 (correct)
        [InlineData(99.5, 99)]   // Truncates to 99 (incorrect, should be 100)
        [InlineData(99.6, 99)]   // Truncates to 99 (incorrect, should be 100)
        public void TestCommissionTruncation_ShowsOldBehavior(decimal input, long expectedTruncated)
        {
            // Arrange & Act
            // This simulates the old problematic casting logic
            long result = (long)input;
            
            // Assert
            Assert.Equal(expectedTruncated, result);
        }

        [Fact]
        public void TestCommissionSumming_WithRoundingFix()
        {
            // Arrange
            decimal[] commissionValues = { 10.5m, 20.6m, 30.4m, 40.5m };
            
            // Act - Using the fixed rounding approach
            long[] roundedValues = new long[commissionValues.Length];
            for (int i = 0; i < commissionValues.Length; i++)
            {
                roundedValues[i] = (long)Math.Round(commissionValues[i], MidpointRounding.AwayFromZero);
            }
            
            decimal totalFromRounded = roundedValues.Sum();
            
            // Assert
            // Expected: 11 + 21 + 30 + 41 = 103
            Assert.Equal(103m, totalFromRounded);
        }

        [Fact]
        public void TestCommissionSumming_WithOldTruncation()
        {
            // Arrange
            decimal[] commissionValues = { 10.5m, 20.6m, 30.4m, 40.5m };
            
            // Act - Using the old truncation approach
            long[] truncatedValues = new long[commissionValues.Length];
            for (int i = 0; i < commissionValues.Length; i++)
            {
                truncatedValues[i] = (long)commissionValues[i];
            }
            
            decimal totalFromTruncated = truncatedValues.Sum();
            
            // Assert
            // Expected: 10 + 20 + 30 + 40 = 100 (loses 2.0 in total)
            Assert.Equal(100m, totalFromTruncated);
        }

        [Fact]
        public void TestCommissionDifference_ShowsImprovementFromFix()
        {
            // Arrange
            decimal[] commissionValues = { 10.5m, 20.6m, 30.4m, 40.5m, 15.7m, 25.8m };
            
            // Act
            decimal totalOriginal = commissionValues.Sum(); // 143.5
            
            // Old approach (truncation)
            long totalTruncated = commissionValues.Sum(c => (long)c); // 10+20+30+40+15+25 = 140
            
            // New approach (proper rounding)
            long totalRounded = commissionValues.Sum(c => (long)Math.Round(c, MidpointRounding.AwayFromZero)); // 11+21+30+41+16+26 = 145
            
            // Assert
            Assert.Equal(143.5m, totalOriginal);
            Assert.Equal(140, totalTruncated);
            Assert.Equal(145, totalRounded);
            
            // The rounded approach is much closer to the original decimal sum
            decimal truncationError = Math.Abs(totalOriginal - totalTruncated); // 3.5
            decimal roundingError = Math.Abs(totalOriginal - totalRounded);     // 1.5
            
            Assert.True(roundingError < truncationError, "Rounding should be more accurate than truncation");
        }
    }
}
